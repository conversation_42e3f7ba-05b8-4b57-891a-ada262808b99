---
import MainLayout from '../../layouts/main.astro';
---

<MainLayout title="Quick Start Guide - Gitea Mirror">
  <main class="max-w-5xl mx-auto px-4 py-12">
    <div class="sticky top-4 z-10 mb-6">
      <a
        href="/docs/"
        class="inline-flex items-center gap-2 px-3 py-1.5 rounded-md bg-card text-foreground hover:bg-muted transition-colors border border-border focus:ring-2 focus:ring-ring outline-none"
      >
        <span aria-hidden="true">&larr;</span> Back to Documentation
      </a>
    </div>

    <article class="bg-card rounded-2xl shadow-lg p-6 md:p-8 border border-border">
      <!-- Header -->
      <div class="mb-12 space-y-4">
        <div class="flex items-center gap-2 text-sm text-muted-foreground mb-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
          </svg>
          <span>Quick Start</span>
        </div>
        <h1 class="text-4xl font-bold tracking-tight">Gitea Mirror Quick Start Guide</h1>
        <p class="text-lg text-muted-foreground leading-relaxed max-w-4xl">
          This guide will help you get Gitea Mirror up and running quickly.
        </p>
      </div>

      <!-- Prerequisites -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Prerequisites</h2>
        
        <div class="bg-gradient-to-r from-primary/5 to-transparent rounded-lg p-6 border-l-4 border-primary mb-8">
          <h3 class="font-semibold mb-4">Before you begin, make sure you have:</h3>
          
          <div class="space-y-3">
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold">1</div>
              <div>
                <span class="font-semibold text-foreground">A GitHub account with a personal access token</span>
                <p class="text-sm text-muted-foreground mt-1">Create one at <a href="https://github.com/settings/tokens" class="text-primary hover:underline">GitHub Settings</a></p>
              </div>
            </div>
            
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
              <div>
                <span class="font-semibold text-foreground">A Gitea instance with an access token</span>
                <p class="text-sm text-muted-foreground mt-1">Available in your Gitea Settings > Applications</p>
              </div>
            </div>
            
            <div class="flex items-start gap-3">
              <div class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
              <div>
                <span class="font-semibold text-foreground">One of the following:</span>
                <ul class="mt-2 space-y-1 text-sm text-muted-foreground pl-4">
                  <li class="flex gap-2"><span>•</span> Docker and docker-compose (for Docker deployment)</li>
                  <li class="flex gap-2"><span>•</span> Bun 1.2.9+ (for native deployment)</li>
                  <li class="flex gap-2"><span>•</span> Proxmox VE or LXD (for LXC container deployment)</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Installation Options -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Installation Options</h2>
        <p class="text-muted-foreground mb-8">Choose the installation method that works best for your environment.</p>

        <!-- Docker Installation -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4 flex items-center gap-2">
            🐳 Using Docker <span class="text-sm text-muted-foreground font-normal">(Recommended for most users)</span>
          </h3>
          
          <div class="bg-card rounded-lg border border-border p-6">
            <p class="text-muted-foreground mb-4">Docker provides the easiest way to get started with minimal configuration.</p>
            
            <div class="space-y-4">
              {[
                { step: 'Clone the repository', cmd: 'git clone https://github.com/RayLabsHQ/gitea-mirror.git\ncd gitea-mirror' },
                { step: 'Start the application in production mode', cmd: 'docker compose up -d' },
                { step: 'Access the application', cmd: null, text: 'Open your browser and navigate to <a href="http://localhost:4321" class="text-primary hover:underline font-medium">http://localhost:4321</a>' }
              ].map((item, i) => (
                <div>
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm font-semibold text-muted-foreground">STEP {i + 1}</span>
                    <span class="text-sm text-muted-foreground">{item.step}</span>
                  </div>
                  {item.cmd ? (
                    <div class="bg-muted/30 rounded-lg p-4">
                      <pre class="text-sm"><code>{item.cmd}</code></pre>
                    </div>
                  ) : (
                    <p class="text-sm" set:html={item.text}></p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        <!-- Bun Installation -->
        <div class="mb-8">
          <h3 class="text-xl font-semibold mb-4 flex items-center gap-2">
            🏃 Using Bun <span class="text-sm text-muted-foreground font-normal">(Native Installation)</span>
          </h3>
          
          <div class="bg-card rounded-lg border border-border p-6">
            <p class="text-muted-foreground mb-4">If you prefer to run the application directly on your system:</p>
            
            <div class="space-y-4">
              <div>
                <div class="flex items-center gap-2 mb-2">
                  <span class="text-sm font-semibold text-muted-foreground">STEP 1</span>
                  <span class="text-sm text-muted-foreground">Clone the repository</span>
                </div>
                <div class="bg-muted/30 rounded-lg p-4">
                  <pre class="text-sm"><code>git clone https://github.com/RayLabsHQ/gitea-mirror.git
cd gitea-mirror</code></pre>
                </div>
              </div>
              
              <div>
                <div class="flex items-center gap-2 mb-2">
                  <span class="text-sm font-semibold text-muted-foreground">STEP 2</span>
                  <span class="text-sm text-muted-foreground">Run the quick setup script</span>
                </div>
                <div class="bg-muted/30 rounded-lg p-4">
                  <pre class="text-sm"><code>bun run setup</code></pre>
                </div>
                <p class="text-sm text-muted-foreground mt-2">This installs dependencies and initializes the database.</p>
              </div>
              
              <div>
                <div class="flex items-center gap-2 mb-2">
                  <span class="text-sm font-semibold text-muted-foreground">STEP 3</span>
                  <span class="text-sm text-muted-foreground">Choose how to run the application</span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                  <div class="border border-border rounded-lg p-4">
                    <h5 class="font-semibold mb-2">Development Mode</h5>
                    <div class="bg-muted/30 rounded-lg p-3 text-sm">
                      <pre><code>bun run dev</code></pre>
                    </div>
                    <p class="text-xs text-muted-foreground mt-2">For Bun-specific features, use: <code class="bg-muted px-1 rounded">bunx --bun astro dev</code></p>
                  </div>
                  
                  <div class="border border-border rounded-lg p-4">
                    <h5 class="font-semibold mb-2">Production Mode</h5>
                    <div class="bg-muted/30 rounded-lg p-3 text-sm">
                      <pre><code>bun run build
bun run start</code></pre>
                    </div>
                  </div>
                </div>
              </div>
              
              <div>
                <div class="flex items-center gap-2 mb-2">
                  <span class="text-sm font-semibold text-muted-foreground">STEP 4</span>
                  <span class="text-sm text-muted-foreground">Access the application</span>
                </div>
                <p class="text-sm">Open your browser and navigate to <a href="http://localhost:4321" class="text-primary hover:underline font-medium">http://localhost:4321</a></p>
              </div>
            </div>
          </div>
        </div>

        <!-- LXC Installation -->
        <div>
          <h3 class="text-xl font-semibold mb-4 flex items-center gap-2">
            📦 Using LXC Containers <span class="text-sm text-muted-foreground font-normal">(Recommended for Proxmox VE)</span>
          </h3>
          
          <div class="bg-card rounded-lg border border-border p-6">
            <div class="space-y-4">
              <h4 class="font-semibold flex items-center gap-2">
                <span>Proxmox VE Installation</span>
                <span class="text-xs text-muted-foreground font-normal">(Community Script)</span>
              </h4>
              
              <p class="text-sm text-muted-foreground mb-4">Deploy Gitea Mirror on Proxmox VE using the community-maintained script:</p>
              
              <div class="bg-muted/30 rounded-lg p-4 overflow-x-auto">
                <pre class="text-sm whitespace-pre-wrap break-all"><code>bash -c "$(curl -fsSL https://raw.githubusercontent.com/community-scripts/ProxmoxVE/main/ct/gitea-mirror.sh)"</code></pre>
              </div>
              
              <div class="mt-4 text-sm text-muted-foreground">
                <p class="font-medium mb-2">This script will:</p>
                <ul class="space-y-1 pl-4">
                  <li class="flex gap-2"><span>•</span> Create a privileged Alpine Linux LXC container</li>
                  <li class="flex gap-2"><span>•</span> Install Bun runtime and dependencies</li>
                  <li class="flex gap-2"><span>•</span> Clone and build Gitea Mirror</li>
                  <li class="flex gap-2"><span>•</span> Configure systemd service for automatic startup</li>
                  <li class="flex gap-2"><span>•</span> Set up the application to run on port 4321</li>
                </ul>
              </div>
              
              <div class="mt-4 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <div class="flex gap-3">
                  <div class="text-blue-600 dark:text-blue-500">
                    <svg class="w-5 h-5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div class="text-sm">
                    <p class="font-semibold text-blue-600 dark:text-blue-500 mb-1">Note</p>
                    <p>After installation, access Gitea Mirror at <code class="bg-blue-500/10 px-1 py-0.5 rounded">http://&lt;container-ip&gt;:4321</code></p>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-border">
              <p class="text-sm text-muted-foreground">
                For more information about the community script, visit the 
                <a href="https://community-scripts.github.io/ProxmoxVE/scripts?id=gitea-mirror" class="text-primary hover:underline">Community Scripts documentation</a>.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Initial Configuration -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Initial Configuration</h2>
        <p class="text-muted-foreground mb-6">Follow these steps to configure Gitea Mirror for first use:</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[
            {
              num: '1',
              title: 'Create Admin Account',
              items: [
                "You'll be prompted on first access",
                'Choose a secure username and password',
                'This will be your administrator account'
              ]
            },
            {
              num: '2',
              title: 'Configure GitHub Connection',
              items: [
                'Navigate to the Configuration page',
                'Enter your GitHub credentials',
                'Select repositories to mirror',
                'Configure filtering options'
              ]
            },
            {
              num: '3',
              title: 'Configure Gitea Connection',
              items: [
                'Enter your Gitea server URL',
                'Enter your Gitea access token',
                'Configure organization settings',
                'Set default visibility'
              ]
            },
            {
              num: '4',
              title: 'Set Up Scheduling',
              items: [
                'Enable automatic mirroring',
                'Set the mirroring interval',
                'Save your configuration'
              ]
            }
          ].map(step => (
            <div class="bg-card rounded-lg border border-border p-4 hover:border-primary/50 transition-colors">
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold">{step.num}</div>
                <div>
                  <h4 class="font-semibold mb-1">{step.title}</h4>
                  <ul class="text-sm text-muted-foreground space-y-1">
                    {step.items.map(item => (
                      <li>• {item}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Performing Your First Mirror -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Performing Your First Mirror</h2>
        
        <div class="bg-gradient-to-r from-primary/5 to-transparent rounded-lg p-6 border-l-4 border-primary">
          <h3 class="font-semibold mb-4">After completing the configuration, you can start mirroring repositories:</h3>
          
          <ol class="space-y-3">
            {[
              'Click <strong>"Import GitHub Data"</strong> to fetch repositories from GitHub',
              'Go to the <strong>Repositories</strong> page to view your imported repositories',
              'Select the repositories you want to mirror',
              'Click <strong>"Mirror Selected"</strong> to start the mirroring process',
              'Monitor the progress on the <strong>Activity</strong> page',
              "You'll receive toast notifications about the success or failure of operations"
            ].map((step, i) => (
              <li class="flex gap-3">
                <span class="flex-shrink-0 w-6 h-6 bg-primary/10 rounded-full flex items-center justify-center text-sm font-semibold">{i + 1}</span>
                <span set:html={step}></span>
              </li>
            ))}
          </ol>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Troubleshooting -->
      <section class="mb-12">
        <h2 class="text-2xl font-bold mb-6">Troubleshooting</h2>
        
        <div class="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4">
          <div class="flex gap-3">
            <div class="text-amber-600 dark:text-amber-500">
              <svg class="w-5 h-5 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
              </svg>
            </div>
            <div class="space-y-3">
              <h4 class="font-semibold text-amber-600 dark:text-amber-500">If you encounter any issues:</h4>
              
              <div class="space-y-2 text-sm">
                <div class="flex gap-2">
                  <span class="text-amber-600 dark:text-amber-500">•</span>
                  <span>Check the <strong>Activity Log</strong> for detailed error messages</span>
                </div>
                <div class="flex gap-2">
                  <span class="text-amber-600 dark:text-amber-500">•</span>
                  <span>Verify your GitHub and Gitea tokens have the correct permissions</span>
                </div>
                <div class="flex gap-2">
                  <span class="text-amber-600 dark:text-amber-500">•</span>
                  <span>Ensure your Gitea instance is accessible from the machine running Gitea Mirror</span>
                </div>
              </div>
              
              <div class="mt-4 space-y-3">
                <h5 class="font-semibold">Check logs based on your deployment method:</h5>
                
                <div class="bg-amber-500/5 rounded-lg p-3 space-y-2">
                  <div class="grid grid-cols-1 gap-2 text-sm">
                    <div>
                      <span class="font-medium">Docker:</span>
                      <code class="bg-amber-500/10 px-1.5 py-0.5 rounded ml-2">docker logs gitea-mirror</code>
                    </div>
                    <div>
                      <span class="font-medium">Native:</span>
                      <span class="text-muted-foreground ml-2">Check terminal output or system logs</span>
                    </div>
                    <div>
                      <span class="font-medium">LXC:</span>
                      <code class="bg-amber-500/10 px-1.5 py-0.5 rounded ml-2">systemctl status gitea-mirror</code>
                      <span class="text-muted-foreground ml-1">or</span>
                      <code class="bg-amber-500/10 px-1.5 py-0.5 rounded ml-1">journalctl -u gitea-mirror -f</code>
                    </div>
                  </div>
                </div>
                
                <div class="space-y-2 text-sm">
                  <div class="flex gap-2">
                    <span class="text-amber-600 dark:text-amber-500">•</span>
                    <span>Use the health check endpoint: <code class="bg-amber-500/10 px-1.5 py-0.5 rounded">curl http://your-server:4321/api/health</code></span>
                  </div>
                  <div class="flex gap-2">
                    <span class="text-amber-600 dark:text-amber-500">•</span>
                    <span>For database issues: <code class="bg-amber-500/10 px-1.5 py-0.5 rounded">bun run check-db</code> or <code class="bg-amber-500/10 px-1.5 py-0.5 rounded">bun run fix-db</code></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="my-12 h-px bg-border/50"></div>

      <!-- Next Steps -->
      <section>
        <h2 class="text-2xl font-bold mb-6">Next Steps</h2>
        
        <div class="bg-gradient-to-br from-primary/5 via-transparent to-primary/5 rounded-lg p-6 border border-border">
          <h3 class="font-semibold mb-4">After your initial setup:</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-3">
              {[
                'Explore the dashboard for an overview of your mirroring status',
                'Set up automatic mirroring schedules for hands-off operation',
                'Configure organization mirroring for team repositories',
                'Use the cleanup button in Activity Log to manage old events'
              ].map(item => (
                <div class="flex gap-3">
                  <span class="text-primary">✓</span>
                  <span>{item}</span>
                </div>
              ))}
            </div>
            
            <div class="space-y-3">
              <div class="flex gap-3">
                <span class="text-primary">📖</span>
                <span>Check out the <a href="/docs/configuration" class="text-primary hover:underline font-medium">Configuration Guide</a> for advanced settings</span>
              </div>
              <div class="flex gap-3">
                <span class="text-primary">🏗️</span>
                <span>Review the <a href="/docs/architecture" class="text-primary hover:underline font-medium">Architecture Documentation</a> to understand the system</span>
              </div>
              <div class="flex gap-3">
                <span class="text-primary">📊</span>
                <span>For server deployments, set up monitoring using the health check endpoint</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </article>
  </main>
</MainLayout>