# Development environment with local Gitea instance for testing
# Run with: docker compose -f docker-compose.dev.yml up -d

services:
  # Local Gitea instance for testing
  gitea:
    image: gitea/gitea:latest
    container_name: gitea
    restart: unless-stopped
    entrypoint: ["/tmp/gitea-dev-init.sh"]
    environment:
      - USER_UID=1000
      - USER_GID=1000
      - GITEA__database__DB_TYPE=sqlite3
      - GITEA__database__PATH=/data/gitea/gitea.db
      - GITEA__server__DOMAIN=localhost
      - GITEA__server__ROOT_URL=http://localhost:3001/
      - GITEA__server__SSH_DOMAIN=localhost
      - GITEA__server__SSH_PORT=2222
      - GITEA__server__START_SSH_SERVER=true
      - GITEA__security__INSTALL_LOCK=true
      - GITEA__service__DISABLE_REGISTRATION=false
      - GITEA__log__MODE=console
      - GITEA__log__LEVEL=Info
    ports:
      - "3001:3000"
      - "2222:22"
    volumes:
      - gitea-data:/data
      - gitea-config:/etc/gitea
      - ./scripts/gitea-app.ini:/tmp/app.ini:ro
      - ./scripts/gitea-dev-init.sh:/tmp/gitea-dev-init.sh:ro
    networks:
      - gitea-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Development service connected to local Gitea
  gitea-mirror-dev:
    # For dev environment, always build from local sources
    build:
      context: .
      dockerfile: Dockerfile
      platforms:
        - linux/amd64
        - linux/arm64
    container_name: gitea-mirror-dev
    restart: unless-stopped
    ports:
      - "4321:4321"
    volumes:
      - gitea-mirror-data:/app/data
      # Mount custom CA certificates - choose one option:
      # Option 1: Mount individual CA certificates from certs directory
      # - ./certs:/app/certs:ro
      # Option 2: Mount system CA bundle (if your CA is already in system store)
      # - /etc/ssl/certs/ca-certificates.crt:/etc/ssl/certs/ca-certificates.crt:ro
    depends_on:
      - gitea
    environment:
      - NODE_ENV=development
      - DATABASE_URL=file:data/gitea-mirror.db
      - HOST=0.0.0.0
      - PORT=4321
      - JWT_SECRET=dev-secret-key
      # GitHub/Gitea Mirror Config
      - GITHUB_USERNAME=${GITHUB_USERNAME:-your-github-username}
      - GITHUB_TOKEN=${GITHUB_TOKEN:-your-github-token}
      - SKIP_FORKS=${SKIP_FORKS:-false}
      - PRIVATE_REPOSITORIES=${PRIVATE_REPOSITORIES:-false}
      - MIRROR_ISSUES=${MIRROR_ISSUES:-false}
      - MIRROR_WIKI=${MIRROR_WIKI:-false}
      - MIRROR_STARRED=${MIRROR_STARRED:-false}
      - MIRROR_ORGANIZATIONS=${MIRROR_ORGANIZATIONS:-false}
      - PRESERVE_ORG_STRUCTURE=${PRESERVE_ORG_STRUCTURE:-false}
      - ONLY_MIRROR_ORGS=${ONLY_MIRROR_ORGS:-false}
      - SKIP_STARRED_ISSUES=${SKIP_STARRED_ISSUES:-false}
      - GITEA_URL=http://gitea:3000
      - GITEA_TOKEN=${GITEA_TOKEN:-your-local-gitea-token}
      - GITEA_USERNAME=${GITEA_USERNAME:-your-local-gitea-username}
      - GITEA_ORGANIZATION=${GITEA_ORGANIZATION:-github-mirrors}
      - GITEA_ORG_VISIBILITY=${GITEA_ORG_VISIBILITY:-public}
      - DELAY=${DELAY:-3600}
      # Optional: Skip TLS verification (insecure, use only for testing)
      # - GITEA_SKIP_TLS_VERIFY=${GITEA_SKIP_TLS_VERIFY:-false}
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4321/api/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 5s
    networks:
      - gitea-network



# Define named volumes for data persistence
volumes:
  gitea-data:    # Gitea data volume
  gitea-config:  # Gitea config volume
  gitea-mirror-data:  # Gitea Mirror database volume

# Define networks
networks:
  gitea-network:
    name: gitea-network
    # Let Docker Compose manage this network
    # If you need to use an existing network, uncomment the line below
    # external: true
