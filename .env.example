# Docker Registry Configuration
DOCKER_REGISTRY=ghcr.io
DOCKER_IMAGE=arunavo4/gitea-mirror
DOCKER_TAG=latest

# Application Configuration
NODE_ENV=production
HOST=0.0.0.0
PORT=4321
DATABASE_URL=sqlite://data/gitea-mirror.db

# Security
JWT_SECRET=change-this-to-a-secure-random-string-in-production

# Optional GitHub/Gitea Mirror Configuration (for docker-compose, can also be set via web UI)
# Uncomment and set as needed. These are passed as environment variables to the container.
# GITHUB_USERNAME=your-github-username
# GITHUB_TOKEN=your-github-personal-access-token
# SKIP_FORKS=false
# PRIVATE_REPOSITORIES=false
# MIRROR_ISSUES=false
# MIRROR_WIKI=false
# MIRROR_STARRED=false
# MIRROR_ORGANIZATIONS=false
# PRESERVE_ORG_STRUCTURE=false
# ONLY_MIRROR_ORGS=false
# SKIP_STARRED_ISSUES=false
# GITEA_URL=http://gitea:3000
# GITEA_TOKEN=your-local-gitea-token
# GITEA_USERNAME=your-local-gitea-username
# GITEA_ORGANIZATION=github-mirrors
# GITEA_ORG_VISIBILITY=public
# DELAY=3600

# Optional Database Cleanup Configuration (configured via web UI)
# These environment variables are optional and only used as defaults
# Users can configure cleanup settings through the web interface
# CLEANUP_ENABLED=false
# CLEANUP_RETENTION_DAYS=7

# Optional TLS/SSL Configuration
# Option 1: Mount custom CA certificates in ./certs directory as .crt files
#          The container will automatically combine them into a CA bundle
# Option 2: Mount your system CA bundle at /etc/ssl/certs/ca-certificates.crt
#          See docker-compose.yml for volume mount examples
# GITEA_SKIP_TLS_VERIFY=false  # WARNING: Only use for testing, disables TLS verification
